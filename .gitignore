# DreamBig Real Estate Platform - Git Ignore File

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual Environment
venv/
env/
ENV/
env.bak/
venv.bak/
.venv/

# Database Files
*.db
*.sqlite
*.sqlite3
sql_app.db
test.db

# Environment Variables
.env
.env.local
.env.development
.env.test
.env.production

# Firebase Credentials (IMPORTANT: Keep these secure!)
*firebase*credential*.json
*serviceAccount*.json
firebase-adminsdk-*.json

# Temporary Files
temp_token.txt
*.tmp
*.temp
.cache/
.pytest_cache/

# IDE and Editor Files
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store
Thumbs.db

# Logs
*.log
logs/
log/

# Node.js (if using any frontend build tools)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Coverage Reports
htmlcov/
.coverage
.coverage.*
coverage.xml
*.cover
.hypothesis/

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# Local development
local_settings.py
dev_settings.py

# Backup files
*.bak
*.backup

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Media uploads (if any)
uploads/
media/
static/uploads/

# Documentation builds
docs/_build/
site/

# Custom exclusions for this project
scripts/__pycache__/
app/__pycache__/
app/*/__pycache__/
app/*/*/__pycache__/

# Sensitive configuration
config/production.py
config/secrets.py

# Deployment files
deploy/
deployment/
docker-compose.override.yml

# Local development database
*.db-journal
*.db-wal
*.db-shm