# Database Configuration
DATABASE_URL=sqlite:///./sql_app.db
# For PostgreSQL: postgresql://user:password@localhost/dreambig
# For MySQL: mysql://user:password@localhost/dreambig

# Firebase Configuration
FIREBASE_CREDENTIALS=app/dreambig_firebase_credentioal.json

# Security Settings
SECRET_KEY=your-super-secret-key-change-this-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Email Configuration (Gmail example)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password
EMAILS_FROM_EMAIL=<EMAIL>
EMAIL_TEMPLATES_DIR=app/templates/email

# SMS Configuration (Twilio)
TWILIO_ACCOUNT_SID=your-twilio-account-sid
TWILIO_AUTH_TOKEN=your-twilio-auth-token
TWILIO_PHONE_NUMBER=+**********

# Redis Configuration (for caching and sessions)
REDIS_URL=redis://localhost:6379/0
REDIS_PASSWORD=
REDIS_DB=0

# External API Keys
GOOGLE_MAPS_API_KEY=your-google-maps-api-key
GOOGLE_PLACES_API_KEY=your-google-places-api-key

# Application Settings
DEBUG=True
ENVIRONMENT=development
CORS_ORIGINS=["http://localhost:3000", "http://localhost:8000", "http://127.0.0.1:8000"]

# File Upload Settings
MAX_FILE_SIZE=********  # 10MB in bytes
ALLOWED_IMAGE_EXTENSIONS=jpg,jpeg,png,gif,webp
ALLOWED_VIDEO_EXTENSIONS=mp4,avi,mov,wmv
UPLOAD_DIR=app/static/uploads

# Rate Limiting
RATE_LIMIT_PER_MINUTE=60
RATE_LIMIT_BURST=10

# Logging
LOG_LEVEL=INFO
LOG_FILE=logs/app.log

# Monitoring
SENTRY_DSN=your-sentry-dsn-here
ENABLE_METRICS=True

# Social Media Login (Optional)
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
FACEBOOK_APP_ID=your-facebook-app-id
FACEBOOK_APP_SECRET=your-facebook-app-secret

# Backup Settings
BACKUP_ENABLED=True
BACKUP_SCHEDULE=0 2 * * *  # Daily at 2 AM
BACKUP_RETENTION_DAYS=30

# Performance Settings
CACHE_TTL=3600  # 1 hour
DATABASE_POOL_SIZE=10
DATABASE_MAX_OVERFLOW=20

# Feature Flags
ENABLE_CHAT=True
ENABLE_NOTIFICATIONS=True
ENABLE_ANALYTICS=True
ENABLE_DARK_MODE=True
ENABLE_PWA=True

# Development Settings
RELOAD=True
HOST=0.0.0.0
PORT=8000
