name: Tests

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: [3.9, 3.10, 3.11]
    
    services:
      postgres:
        image: postgres:13
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: dreambig_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}
    
    - name: Cache pip dependencies
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ hashFiles('**/requirements.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install pytest-xdist pytest-cov
    
    - name: Set up environment variables
      run: |
        echo "TESTING=true" >> $GITHUB_ENV
        echo "DATABASE_URL=postgresql://postgres:postgres@localhost:5432/dreambig_test" >> $GITHUB_ENV
        echo "SECRET_KEY=test_secret_key_for_github_actions" >> $GITHUB_ENV
        echo "FIREBASE_API_KEY=test_api_key" >> $GITHUB_ENV
        echo "FIREBASE_AUTH_DOMAIN=test.firebaseapp.com" >> $GITHUB_ENV
        echo "FIREBASE_PROJECT_ID=test-project" >> $GITHUB_ENV
    
    - name: Run unit tests
      run: |
        python run_tests.py --type unit --coverage --parallel auto
    
    - name: Run integration tests
      run: |
        python run_tests.py --type integration --parallel auto
    
    - name: Run security tests
      run: |
        python run_tests.py --type security
    
    - name: Upload coverage reports to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
        flags: unittests
        name: codecov-umbrella
        fail_ci_if_error: false

  performance-test:
    runs-on: ubuntu-latest
    needs: test
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python 3.11
      uses: actions/setup-python@v4
      with:
        python-version: 3.11
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
    
    - name: Run performance tests
      run: |
        python run_tests.py --type performance --verbose
    
    - name: Upload performance results
      uses: actions/upload-artifact@v3
      with:
        name: performance-results
        path: performance_results.json
        if-no-files-found: ignore

  security-scan:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python 3.11
      uses: actions/setup-python@v4
      with:
        python-version: 3.11
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install bandit safety
    
    - name: Run Bandit security scan
      run: |
        bandit -r app/ -f json -o bandit-report.json || true
    
    - name: Run Safety check
      run: |
        safety check --json --output safety-report.json || true
    
    - name: Upload security reports
      uses: actions/upload-artifact@v3
      with:
        name: security-reports
        path: |
          bandit-report.json
          safety-report.json
        if-no-files-found: ignore

  lint:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python 3.11
      uses: actions/setup-python@v4
      with:
        python-version: 3.11
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install flake8 black isort mypy
    
    - name: Run Black formatter check
      run: |
        black --check app/
    
    - name: Run isort import sorting check
      run: |
        isort --check-only app/
    
    - name: Run flake8 linting
      run: |
        flake8 app/ --max-line-length=88 --extend-ignore=E203,W503
    
    - name: Run mypy type checking
      run: |
        mypy app/ --ignore-missing-imports || true
