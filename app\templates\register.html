<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Register - DreamBig Real Estate</title>
    <link rel="stylesheet" href="{{ url_for('static', path='/css/auth.css') }}">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="auth-container">
        <div class="auth-card register-card">
            <div class="auth-header">
                <div class="logo">
                    <i class="fas fa-home"></i>
                    <h1>DreamBig</h1>
                </div>
                <h2>Create Account</h2>
                <p>Join DreamBig and find your perfect property</p>
            </div>

            <div class="auth-form">
                <form id="registerForm">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="name">
                                <i class="fas fa-user"></i>
                                Full Name
                            </label>
                            <input type="text" id="name" name="name" required>
                            <span class="error-message" id="nameError"></span>
                        </div>

                        <div class="form-group">
                            <label for="phone">
                                <i class="fas fa-phone"></i>
                                Phone Number (Optional)
                            </label>
                            <input type="tel" id="phone" name="phone" placeholder="Enter your phone number">
                            <span class="error-message" id="phoneError"></span>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="email">
                            <i class="fas fa-envelope"></i>
                            Email Address
                        </label>
                        <input type="email" id="email" name="email" required>
                        <span class="error-message" id="emailError"></span>
                    </div>

                    <div class="form-group">
                        <label for="password">
                            <i class="fas fa-lock"></i>
                            Password
                        </label>
                        <div class="password-input">
                            <input type="password" id="password" name="password" required>
                            <button type="button" class="password-toggle" onclick="togglePassword('password')">
                                <i class="fas fa-eye" id="passwordToggleIcon"></i>
                            </button>
                        </div>
                        <div class="password-strength" id="passwordStrength"></div>
                        <span class="error-message" id="passwordError"></span>
                    </div>

                    <div class="form-group">
                        <label for="confirmPassword">
                            <i class="fas fa-lock"></i>
                            Confirm Password
                        </label>
                        <div class="password-input">
                            <input type="password" id="confirmPassword" name="confirmPassword" required>
                            <button type="button" class="password-toggle" onclick="togglePassword('confirmPassword')">
                                <i class="fas fa-eye" id="confirmPasswordToggleIcon"></i>
                            </button>
                        </div>
                        <span class="error-message" id="confirmPasswordError"></span>
                    </div>

                    <div class="form-group">
                        <label for="role">
                            <i class="fas fa-user-tag"></i>
                            I am a
                        </label>
                        <select id="role" name="role" required>
                            <option value="">Select your role</option>
                            <option value="tenant">Tenant (Looking for property)</option>
                            <option value="owner">Owner (Have property to rent/sell)</option>
                            <option value="agent">Agent (Real estate professional)</option>
                        </select>
                        <span class="error-message" id="roleError"></span>
                    </div>

                    <div class="form-group checkbox-group">
                        <label class="checkbox-container">
                            <input type="checkbox" id="termsAccepted" required>
                            <span class="checkmark"></span>
                            I agree to the <a href="#" target="_blank">Terms of Service</a> and <a href="#" target="_blank">Privacy Policy</a>
                        </label>
                        <span class="error-message" id="termsError"></span>
                    </div>

                    <div class="form-group checkbox-group">
                        <label class="checkbox-container">
                            <input type="checkbox" id="marketingEmails">
                            <span class="checkmark"></span>
                            I want to receive marketing emails and updates
                        </label>
                    </div>

                    <button type="submit" class="auth-button" id="registerButton">
                        <span class="button-text">Create Account</span>
                        <div class="loading-spinner" id="registerSpinner"></div>
                    </button>
                </form>

                <div class="divider">
                    <span>or</span>
                </div>

                <button class="google-button" id="googleSignUp">
                    <i class="fab fa-google"></i>
                    Sign up with Google
                </button>

                <div class="auth-footer">
                    <p>Already have an account? <a href="/login">Sign in</a></p>
                </div>
            </div>
        </div>

        <!-- Success/Error Messages -->
        <div class="message-container" id="messageContainer"></div>
    </div>

    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-auth-compat.js"></script>
    
    <!-- Custom JavaScript -->
    <script src="{{ url_for('static', path='/js/firebase-config.js') }}"></script>
    <script src="{{ url_for('static', path='/js/auth.js') }}"></script>
    <script>
        // Initialize register page
        document.addEventListener('DOMContentLoaded', function() {
            initializeRegister();
        });
    </script>
</body>
</html>
