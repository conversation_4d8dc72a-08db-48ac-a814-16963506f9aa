<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chat - DreamBig</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f5f5;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        .chat-header {
            background-color: #e74c3c;
            color: white;
            padding: 1rem;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .chat-container {
            display: flex;
            flex: 1;
            overflow: hidden;
        }
        
        .chat-sidebar {
            width: 300px;
            background-color: white;
            border-right: 1px solid #ddd;
            display: flex;
            flex-direction: column;
        }
        
        .sidebar-header {
            padding: 1rem;
            background-color: #f8f9fa;
            border-bottom: 1px solid #ddd;
            font-weight: bold;
        }
        
        .room-list {
            flex: 1;
            overflow-y: auto;
        }
        
        .room-item {
            padding: 1rem;
            border-bottom: 1px solid #eee;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        
        .room-item:hover {
            background-color: #f8f9fa;
        }
        
        .room-item.active {
            background-color: #e74c3c;
            color: white;
        }
        
        .room-name {
            font-weight: bold;
            margin-bottom: 0.25rem;
        }
        
        .room-description {
            font-size: 0.875rem;
            color: #666;
        }
        
        .room-item.active .room-description {
            color: #fff;
        }
        
        .chat-main {
            flex: 1;
            display: flex;
            flex-direction: column;
            background-color: white;
        }
        
        .chat-messages {
            flex: 1;
            padding: 1rem;
            overflow-y: auto;
            background-color: #fafafa;
        }
        
        .message {
            margin-bottom: 1rem;
            display: flex;
            align-items: flex-start;
        }
        
        .message.own {
            justify-content: flex-end;
        }
        
        .message-content {
            max-width: 70%;
            padding: 0.75rem 1rem;
            border-radius: 1rem;
            background-color: white;
            box-shadow: 0 1px 2px rgba(0,0,0,0.1);
        }
        
        .message.own .message-content {
            background-color: #e74c3c;
            color: white;
        }
        
        .message-sender {
            font-weight: bold;
            font-size: 0.875rem;
            margin-bottom: 0.25rem;
        }
        
        .message.own .message-sender {
            color: #fff;
        }
        
        .message-text {
            word-wrap: break-word;
        }
        
        .message-time {
            font-size: 0.75rem;
            color: #666;
            margin-top: 0.25rem;
        }
        
        .message.own .message-time {
            color: #fff;
        }
        
        .chat-input {
            padding: 1rem;
            border-top: 1px solid #ddd;
            background-color: white;
        }
        
        .input-container {
            display: flex;
            gap: 0.5rem;
        }
        
        .message-input {
            flex: 1;
            padding: 0.75rem;
            border: 1px solid #ddd;
            border-radius: 2rem;
            outline: none;
            font-size: 1rem;
        }
        
        .message-input:focus {
            border-color: #e74c3c;
        }
        
        .send-button {
            padding: 0.75rem 1.5rem;
            background-color: #e74c3c;
            color: white;
            border: none;
            border-radius: 2rem;
            cursor: pointer;
            font-weight: bold;
            transition: background-color 0.2s;
        }
        
        .send-button:hover {
            background-color: #c0392b;
        }
        
        .send-button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        
        .online-users {
            padding: 1rem;
            border-top: 1px solid #ddd;
            background-color: #f8f9fa;
        }
        
        .online-user {
            display: flex;
            align-items: center;
            margin-bottom: 0.5rem;
        }
        
        .online-indicator {
            width: 8px;
            height: 8px;
            background-color: #28a745;
            border-radius: 50%;
            margin-right: 0.5rem;
        }
        
        .typing-indicator {
            padding: 0.5rem 1rem;
            font-style: italic;
            color: #666;
            font-size: 0.875rem;
        }
        
        .connection-status {
            padding: 0.5rem 1rem;
            text-align: center;
            font-size: 0.875rem;
        }
        
        .connection-status.connected {
            background-color: #d4edda;
            color: #155724;
        }
        
        .connection-status.disconnected {
            background-color: #f8d7da;
            color: #721c24;
        }
        
        .no-room-selected {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100%;
            color: #666;
            font-size: 1.125rem;
        }
        
        @media (max-width: 768px) {
            .chat-sidebar {
                width: 100%;
                position: absolute;
                z-index: 1000;
                height: 100%;
                transform: translateX(-100%);
                transition: transform 0.3s;
            }
            
            .chat-sidebar.open {
                transform: translateX(0);
            }
            
            .mobile-menu-button {
                display: block;
                position: absolute;
                left: 1rem;
                top: 50%;
                transform: translateY(-50%);
                background: none;
                border: none;
                color: white;
                font-size: 1.5rem;
                cursor: pointer;
            }
        }
        
        .mobile-menu-button {
            display: none;
        }
    </style>
</head>
<body>
    <div class="chat-header">
        <button class="mobile-menu-button" onclick="toggleSidebar()">☰</button>
        <h1>🏠 DreamBig Chat</h1>
    </div>
    
    <div class="connection-status" id="connectionStatus">
        Connecting...
    </div>
    
    <div class="chat-container">
        <div class="chat-sidebar" id="chatSidebar">
            <div class="sidebar-header">
                Chat Rooms
            </div>
            <div class="room-list" id="roomList">
                <!-- Rooms will be loaded here -->
            </div>
            <div class="online-users">
                <strong>Online Users</strong>
                <div id="onlineUsersList">
                    <!-- Online users will be loaded here -->
                </div>
            </div>
        </div>
        
        <div class="chat-main">
            <div id="noRoomSelected" class="no-room-selected">
                Select a chat room to start messaging
            </div>
            
            <div id="chatArea" style="display: none; flex: 1; display: flex; flex-direction: column;">
                <div class="chat-messages" id="chatMessages">
                    <!-- Messages will be loaded here -->
                </div>
                
                <div class="typing-indicator" id="typingIndicator" style="display: none;">
                    Someone is typing...
                </div>
                
                <div class="chat-input">
                    <div class="input-container">
                        <input 
                            type="text" 
                            class="message-input" 
                            id="messageInput" 
                            placeholder="Type your message..."
                            onkeypress="handleKeyPress(event)"
                        >
                        <button class="send-button" id="sendButton" onclick="sendMessage()">
                            Send
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let ws = null;
        let currentRoom = null;
        let currentUser = null;
        let typingTimer = null;
        
        // Initialize chat
        document.addEventListener('DOMContentLoaded', function() {
            // Get user token from localStorage or session
            const token = localStorage.getItem('firebase_token') || sessionStorage.getItem('firebase_token');
            if (!token) {
                alert('Please login to access chat');
                window.location.href = '/login';
                return;
            }
            
            connectWebSocket(token);
            loadChatRooms();
        });
        
        function connectWebSocket(token) {
            const wsUrl = `ws://localhost:8000/api/v1/chat/ws/${token}`;
            ws = new WebSocket(wsUrl);
            
            ws.onopen = function() {
                updateConnectionStatus('connected');
                requestOnlineUsers();
            };
            
            ws.onmessage = function(event) {
                const data = JSON.parse(event.data);
                handleWebSocketMessage(data);
            };
            
            ws.onclose = function() {
                updateConnectionStatus('disconnected');
                // Attempt to reconnect after 3 seconds
                setTimeout(() => {
                    const token = localStorage.getItem('firebase_token') || sessionStorage.getItem('firebase_token');
                    if (token) {
                        connectWebSocket(token);
                    }
                }, 3000);
            };
            
            ws.onerror = function(error) {
                console.error('WebSocket error:', error);
                updateConnectionStatus('disconnected');
            };
        }
        
        function handleWebSocketMessage(data) {
            switch(data.type) {
                case 'chat_message':
                    displayMessage(data);
                    break;
                case 'online_users':
                    updateOnlineUsers(data.users);
                    break;
                case 'user_status':
                    updateUserStatus(data);
                    break;
                case 'typing':
                    handleTypingIndicator(data);
                    break;
                case 'notification':
                    showNotification(data);
                    break;
                default:
                    console.log('Unknown message type:', data.type);
            }
        }
        
        function updateConnectionStatus(status) {
            const statusElement = document.getElementById('connectionStatus');
            statusElement.className = `connection-status ${status}`;
            statusElement.textContent = status === 'connected' ? 'Connected' : 'Disconnected';
        }
        
        function toggleSidebar() {
            const sidebar = document.getElementById('chatSidebar');
            sidebar.classList.toggle('open');
        }
        
        // Additional JavaScript functions would be implemented here
        // This is a basic template to get started
        
        console.log('Chat interface loaded. WebSocket connection will be established.');
    </script>
</body>
</html>
