[tool:pytest]
# Pytest configuration for DreamBig Real Estate Platform

# Test discovery
testpaths = app/tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*

# Minimum version
minversion = 7.0

# Add options
addopts = 
    --strict-markers
    --strict-config
    --disable-warnings
    --tb=short
    --maxfail=10
    --durations=10

# Markers for test categorization
markers =
    unit: Unit tests for individual components
    integration: Integration tests for API endpoints
    performance: Performance and load tests
    security: Security and vulnerability tests
    slow: Tests that take a long time to run
    database: Tests that require database access
    external: Tests that require external services
    auth: Authentication and authorization tests
    crud: CRUD operation tests
    business_rules: Business logic tests
    api: API endpoint tests
    models: Database model tests
    
# Test timeout (in seconds)
timeout = 300

# Parallel execution
# Use with: pytest -n auto
# Requires pytest-xdist

# Coverage configuration
# Use with: pytest --cov=app
# Requires pytest-cov

# Logging configuration
log_cli = true
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S

# Filter warnings
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::UserWarning:sqlalchemy.*
    ignore::UserWarning:passlib.*

# Environment variables for testing
env =
    TESTING = true
    DATABASE_URL = sqlite:///./test.db
    SECRET_KEY = test_secret_key_for_testing_only
    ALGORITHM = HS256
    ACCESS_TOKEN_EXPIRE_MINUTES = 30
