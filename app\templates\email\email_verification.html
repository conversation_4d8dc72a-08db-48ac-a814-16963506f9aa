<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Verify Your Email - DreamBig</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background-color: #27ae60;
            color: white;
            padding: 20px;
            text-align: center;
            border-radius: 5px 5px 0 0;
        }
        .content {
            background-color: #f8f9fa;
            padding: 30px;
            border-radius: 0 0 5px 5px;
        }
        .button {
            display: inline-block;
            background-color: #27ae60;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 5px;
            margin: 20px 0;
        }
        .footer {
            text-align: center;
            margin-top: 30px;
            color: #666;
            font-size: 14px;
        }
        .warning {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Verify Your Email Address</h1>
    </div>
    
    <div class="content">
        <h2>Hello {{ name }}!</h2>
        
        <p>Thank you for signing up with DreamBig! To complete your registration and secure your account, please verify your email address.</p>
        
        <p>Click the button below to verify your email:</p>
        
        <a href="{{ verification_url }}" class="button">Verify Email Address</a>
        
        <div class="warning">
            <strong>Important:</strong> This verification link will expire in 48 hours for security reasons. If you don't verify your email within this time, you'll need to request a new verification email.
        </div>
        
        <p>If the button above doesn't work, you can copy and paste the following link into your browser:</p>
        <p style="word-break: break-all; background-color: #e9ecef; padding: 10px; border-radius: 3px;">{{ verification_url }}</p>
        
        <p>If you didn't create an account with DreamBig, please ignore this email or contact our support team.</p>
        
        <p>Need help? Contact us at <a href="mailto:{{ support_email }}">{{ support_email }}</a></p>
        
        <p>Best regards,<br>
        The DreamBig Team</p>
    </div>
    
    <div class="footer">
        <p>This email was sent to verify your email address for your DreamBig account.</p>
        <p>© 2024 DreamBig. All rights reserved.</p>
    </div>
</body>
</html>
