<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" width="32" height="32">
  <defs>
    <linearGradient id="grad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background circle -->
  <circle cx="16" cy="16" r="15" fill="url(#grad)" stroke="#fff" stroke-width="1"/>
  
  <!-- House icon -->
  <path d="M8 20h16v8H8z" fill="#fff" opacity="0.9"/>
  <path d="M6 18l10-8 10 8v2H6z" fill="#fff"/>
  <rect x="12" y="22" width="3" height="4" fill="url(#grad)"/>
  <rect x="17" y="22" width="3" height="3" fill="url(#grad)"/>
  
  <!-- Roof detail -->
  <path d="M16 10l8 6.4V18l-8-6.4L8 18v-1.6z" fill="#fff" opacity="0.8"/>
</svg>
